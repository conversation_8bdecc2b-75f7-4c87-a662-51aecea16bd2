import React, { useEffect, useState } from "react";
import { Box, Typography, Badge } from "@mui/material";
import type { Route } from "./+types/ranking";

type Movie = {
  id: number;
  title: string;
  poster_path: string | null;
};

export async function clientLoader({ params }: Route.ClientLoaderArgs) {
  const API_TOKEN = import.meta.env.VITE_TMDB_API_TOKEN;
  const rankingUrl =
    "https://api.themoviedb.org/3/movie/popular?language=ja-JP&page=1";
  const options = {
    method: "GET",
    headers: {
      accept: "application/json",
      Authorization: `Bearer ${API_TOKEN}`,
    },
  };
  const res = await fetch(`${rankingUrl}`, options);
  const json = await res.json();
  console.log(json);
  const ranking: Movie[] = json.results;
  console.log(ranking);
  return { ranking };
}

export function HydrateFallback() {
  return <div>Loading...</div>;
}

export default function Ranking({ loaderData }: Route.ComponentProps) {
  const { ranking } = loaderData;
  return (
    <Box sx={{ width: "100%" }}>
      <Typography variant="h2">Ranking</Typography>
      <Box
        sx={{
          gap: 2,
          m: 2,
          display: "flex",
          flexDirection: "row",
          flexWrap: "nowrap",
          overflow: "auto",
          whiteSpace: "eclipsis",
        }}
      >
        {ranking.map((movie: any) => (
          <Box
            key={movie.id}
            sx={{
              display: "flex",
              flexDirection: "column",
              p: 2,
              height: "300px",
            }}
          >
            {ranking.indexOf(movie) < 3 && (
              <Badge
                badgeContent={ranking.indexOf(movie) + 1}
                color="primary"
                anchorOrigin={{ vertical: "top", horizontal: "left" }}
              ></Badge>
            )}
            <img
              src={`https://image.tmdb.org/t/p/w500${movie.poster_path}`}
              style={{ height: "100%" }}
            />
          </Box>
        ))}
      </Box>
    </Box>
  );
}
