import { Box } from "@mui/material";
import { Outlet, Link } from "react-router";

export default function Menu() {
  const menuItems = [
    {
      name: "映画評価メモ",
      path: "/",
    },
    { name: "myPage", path: "/mypage" },
  ];

  return (
    <>
      <Box>
        {menuItems.map((item) => (
          <Box>
            <Link to={item.path}>{item.name}</Link>
          </Box>
        ))}
      </Box>
    </>
  );
}
