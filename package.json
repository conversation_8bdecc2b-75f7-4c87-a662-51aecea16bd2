{"name": "my-react-router-app", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/material": "^7.3.1", "@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}